import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';
import { IsOptional, IsString, IsInt, Min, IsUUID, IsArray, IsEnum, Max, IsDate, IsBoolean, ValidateNested } from 'class-validator';
import {
  EntityStatus,
  Prisma,
  EmployeeCountRange,
  FundingStage,
  PricingModel,
  PriceRange,
} from '@generated-prisma';
import { EntityTypeFiltersDto } from './filters/entity-type-filters.dto';

const transformQueryArray = ({ value }: { value: string | string[] }): string[] => {
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(v => String(v).trim()).filter(Boolean);
  }
  return value.split(',').map(s => s.trim()).filter(Boolean);
};

export class ListEntitiesDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    default: 1,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    default: 10,
    type: Number,
    maximum: 100, // Example max limit
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100) // Max limit to prevent abuse
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Filter by entity status',
    enum: EntityStatus,
  })
  @IsOptional()
  @IsEnum(EntityStatus)
  status?: EntityStatus;

  @ApiPropertyOptional({
    description: 'Filter by one or more entity type IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
    type: [String], // Important for Swagger to know it's an array
    example: ['uuid-for-type1', 'uuid-for-type2'], // Example for Swagger
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsUUID('4', { each: true, message: 'Each entity type ID must be a valid UUID version 4.' })
  entityTypeIds?: string[];

  @ApiPropertyOptional({
    description: 'Filter by one or more category IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
    type: [String],
    example: ['uuid-for-category1', 'uuid-for-category2'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsUUID('4', { each: true })
  categoryIds?: string[];

  @ApiPropertyOptional({
    description: 'Filter by one or more tag IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
    type: [String],
    example: ['uuid-for-tag1', 'uuid-for-tag2'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsUUID('4', { each: true })
  tagIds?: string[];

  @ApiPropertyOptional({
    description: 'Filter by one or more feature IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
    type: [String],
    example: ['uuid-for-feature1', 'uuid-for-feature2'],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsUUID('4', { each: true, message: 'Each feature ID must be a valid UUID version 4.' })
  featureIds?: string[];

  @ApiPropertyOptional({
    description: 'Search term to filter by (e.g., in name, short_description, description)',
    example: 'AI tool for images',
  })
  @IsOptional()
  @IsString()
  searchTerm?: string;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    example: 'createdAt', // Other examples: 'name', 'avgRating', 'updatedAt'
    // Consider an enum for allowed sortBy fields to make it more robust
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt'; // Default sort field

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsEnum(Prisma.SortOrder)
  sortOrder?: Prisma.SortOrder = Prisma.SortOrder.desc; // Default sort order

  @ApiPropertyOptional({
    description: 'Filter by submitter user ID (UUID of the user who submitted the entity)',
    example: 'user-uuid-of-submitter',
  })
  @IsOptional()
  @IsUUID('4')
  submitterId?: string;

  @ApiPropertyOptional({
    description: 'Filter entities created on or after this date (YYYY-MM-DD or ISO 8601 string).',
    type: Date,
    format: 'date-time',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  createdAtFrom?: Date;

  @ApiPropertyOptional({
    description: 'Filter entities created on or before this date (YYYY-MM-DD or ISO 8601 string).',
    type: Date,
    format: 'date-time',
    example: '2023-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  createdAtTo?: Date;

  @ApiPropertyOptional({
    description: 'Filter entities by whether they have a free tier. Can be true or false.',
    type: Boolean,
    example: true,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  hasFreeTier?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by one or more employee count ranges.',
    type: [String],
    enum: EmployeeCountRange,
    example: [EmployeeCountRange.C1_10, EmployeeCountRange.C11_50],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(EmployeeCountRange, { each: true })
  employeeCountRanges?: EmployeeCountRange[];

  @ApiPropertyOptional({
    description: 'Filter by one or more funding stages.',
    type: [String],
    enum: FundingStage,
    example: [FundingStage.SEED, FundingStage.SERIES_A],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(FundingStage, { each: true })
  fundingStages?: FundingStage[];

  @ApiPropertyOptional({
    description: 'Search by location summary.',
    example: 'San Francisco',
  })
  @IsOptional()
  @IsString()
  locationSearch?: string;

  @ApiPropertyOptional({
    description: 'Filter by API access (for tools).',
    type: Boolean,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true' || value === true) return true;
    if (value === 'false' || value === false) return false;
    return undefined;
  })
  @IsBoolean()
  apiAccess?: boolean;

  @ApiPropertyOptional({
    description: 'Filter by one or more pricing models.',
    type: [String],
    enum: PricingModel,
    example: [PricingModel.SUBSCRIPTION, PricingModel.PAY_PER_USE],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(PricingModel, { each: true })
  pricingModels?: PricingModel[];

  @ApiPropertyOptional({
    description: 'Filter by one or more price ranges.',
    type: [String],
    enum: PriceRange,
    example: [PriceRange.LOW, PriceRange.MEDIUM],
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsEnum(PriceRange, { each: true })
  priceRanges?: PriceRange[];

  @ApiPropertyOptional({
    description: 'Filter by integrations (e.g., "GitHub", "Slack"). Provide as a comma-separated string.',
    type: [String],
    example: 'GitHub,Slack',
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  integrations?: string[];

  @ApiPropertyOptional({
    description: 'Filter by supported platforms/OS (e.g., "Windows", "macOS"). Provide as a comma-separated string.',
    type: [String],
    example: 'Windows,macOS',
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  platforms?: string[];

  @ApiPropertyOptional({
    description: 'Filter by target audience (e.g., "Developers", "Marketers"). Provide as a comma-separated string.',
    type: [String],
    example: 'Developers,Marketers',
  })
  @IsOptional()
  @Transform(transformQueryArray)
  @IsString({ each: true })
  targetAudience?: string[];

  // ========================================
  // ENHANCED FILTERING - Entity Type Specific
  // ========================================

  @ApiPropertyOptional({
    description: 'Entity type specific filters. Allows filtering by fields specific to each entity type (Course, Job, Hardware, Event, etc.)',
    type: EntityTypeFiltersDto,
    example: {
      course: {
        skill_levels: ['BEGINNER', 'INTERMEDIATE'],
        certificate_available: true
      },
      job: {
        employment_types: ['Full-time', 'Remote'],
        salary_min: 80,
        salary_max: 150
      }
    }
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EntityTypeFiltersDto)
  entity_type_filters?: EntityTypeFiltersDto;
}