import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';

// Import individual filter DTOs
import { CourseFiltersDto } from './course-filters.dto';
import { JobFiltersDto } from './job-filters.dto';
import { HardwareFiltersDto } from './hardware-filters.dto';
import { EventFiltersDto } from './event-filters.dto';
// TODO: Create these filter DTOs in Phase 2
// import { AgencyFiltersDto } from './agency-filters.dto';
// import { SoftwareFiltersDto } from './software-filters.dto';
// import { ResearchPaperFiltersDto } from './research-paper-filters.dto';
// import { PodcastFiltersDto } from './podcast-filters.dto';
// import { CommunityFiltersDto } from './community-filters.dto';
// import { GrantFiltersDto } from './grant-filters.dto';
// import { NewsletterFiltersDto } from './newsletter-filters.dto';

/**
 * Entity Type Specific Filters
 * Allows filtering by fields specific to each entity type
 */
export class EntityTypeFiltersDto {
  @ApiPropertyOptional({
    description: 'Filters specific to Course entities',
    type: CourseFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CourseFiltersDto)
  course?: CourseFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Job entities',
    type: JobFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => JobFiltersDto)
  job?: JobFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Hardware entities',
    type: HardwareFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => HardwareFiltersDto)
  hardware?: HardwareFiltersDto;

  @ApiPropertyOptional({
    description: 'Filters specific to Event entities',
    type: EventFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => EventFiltersDto)
  event?: EventFiltersDto;

  // TODO: Phase 2 - Add remaining entity type filters
  // @ApiPropertyOptional({
  //   description: 'Filters specific to Agency entities',
  //   type: AgencyFiltersDto,
  // })
  // @IsOptional()
  // @ValidateNested()
  // @Type(() => AgencyFiltersDto)
  // agency?: AgencyFiltersDto;

  // Additional entity type filters will be added in Phase 2
  // software?, research_paper?, podcast?, community?, grant?, newsletter?, etc.
}
