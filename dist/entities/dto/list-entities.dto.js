"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ListEntitiesDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const _generated_prisma_1 = require("../../../generated/prisma/index.js");
const entity_type_filters_dto_1 = require("./filters/entity-type-filters.dto");
const transformQueryArray = ({ value }) => {
    if (value === null || value === undefined) {
        return value;
    }
    if (Array.isArray(value)) {
        return value.map(v => String(v).trim()).filter(Boolean);
    }
    return value.split(',').map(s => s.trim()).filter(Boolean);
};
class ListEntitiesDto {
    constructor() {
        this.page = 1;
        this.limit = 10;
        this.sortBy = 'createdAt';
        this.sortOrder = _generated_prisma_1.Prisma.SortOrder.desc;
    }
}
exports.ListEntitiesDto = ListEntitiesDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Page number for pagination',
        default: 1,
        type: Number,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of items per page',
        default: 10,
        type: Number,
        maximum: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by entity status',
        enum: _generated_prisma_1.EntityStatus,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.EntityStatus),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more entity type IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
        type: [String],
        example: ['uuid-for-type1', 'uuid-for-type2'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsUUID)('4', { each: true, message: 'Each entity type ID must be a valid UUID version 4.' }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "entityTypeIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more category IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
        type: [String],
        example: ['uuid-for-category1', 'uuid-for-category2'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "categoryIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more tag IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
        type: [String],
        example: ['uuid-for-tag1', 'uuid-for-tag2'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "tagIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more feature IDs (UUIDs). Provide as comma-separated string or multiple query parameters.',
        type: [String],
        example: ['uuid-for-feature1', 'uuid-for-feature2'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsUUID)('4', { each: true, message: 'Each feature ID must be a valid UUID version 4.' }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "featureIds", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search term to filter by (e.g., in name, short_description, description)',
        example: 'AI tool for images',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "searchTerm", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Field to sort by - supports entity fields and computed values',
        enum: [
            'createdAt', 'updatedAt', 'name', 'foundedYear',
            'averageRating', 'reviewCount', 'saveCount', 'viewCount',
            'relevance', 'popularity'
        ],
        example: 'averageRating',
        default: 'createdAt',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sort order',
        enum: ['asc', 'desc'],
        default: 'desc',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.Prisma.SortOrder),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by submitter user ID (UUID of the user who submitted the entity)',
        example: 'user-uuid-of-submitter',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)('4'),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "submitterId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter entities created on or after this date (YYYY-MM-DD or ISO 8601 string).',
        type: Date,
        format: 'date-time',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ListEntitiesDto.prototype, "createdAtFrom", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter entities created on or before this date (YYYY-MM-DD or ISO 8601 string).',
        type: Date,
        format: 'date-time',
        example: '2023-12-31T23:59:59.999Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Date),
    (0, class_validator_1.IsDate)(),
    __metadata("design:type", Date)
], ListEntitiesDto.prototype, "createdAtTo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter entities by whether they have a free tier. Can be true or false.',
        type: Boolean,
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "hasFreeTier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more employee count ranges.',
        type: [String],
        enum: _generated_prisma_1.EmployeeCountRange,
        example: [_generated_prisma_1.EmployeeCountRange.C1_10, _generated_prisma_1.EmployeeCountRange.C11_50],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.EmployeeCountRange, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "employeeCountRanges", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more funding stages.',
        type: [String],
        enum: _generated_prisma_1.FundingStage,
        example: [_generated_prisma_1.FundingStage.SEED, _generated_prisma_1.FundingStage.SERIES_A],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.FundingStage, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "fundingStages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search by location summary.',
        example: 'San Francisco',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "locationSearch", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by API access (for tools).',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "apiAccess", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more pricing models.',
        type: [String],
        enum: _generated_prisma_1.PricingModel,
        example: [_generated_prisma_1.PricingModel.SUBSCRIPTION, _generated_prisma_1.PricingModel.PAY_PER_USE],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.PricingModel, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "pricingModels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by one or more price ranges.',
        type: [String],
        enum: _generated_prisma_1.PriceRange,
        example: [_generated_prisma_1.PriceRange.LOW, _generated_prisma_1.PriceRange.MEDIUM],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsEnum)(_generated_prisma_1.PriceRange, { each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "priceRanges", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by integrations (e.g., "GitHub", "Slack"). Provide as a comma-separated string.',
        type: [String],
        example: 'GitHub,Slack',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "integrations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by supported platforms/OS (e.g., "Windows", "macOS"). Provide as a comma-separated string.',
        type: [String],
        example: 'Windows,macOS',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "platforms", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by target audience (e.g., "Developers", "Marketers"). Provide as a comma-separated string.',
        type: [String],
        example: 'Developers,Marketers',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(transformQueryArray),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], ListEntitiesDto.prototype, "targetAudience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum average rating (1-5 scale)',
        type: Number,
        minimum: 1,
        maximum: 5,
        example: 4.0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "rating_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum average rating (1-5 scale)',
        type: Number,
        minimum: 1,
        maximum: 5,
        example: 5.0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "rating_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum number of reviews',
        type: Number,
        minimum: 0,
        example: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "review_count_min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum number of reviews',
        type: Number,
        minimum: 0,
        example: 1000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], ListEntitiesDto.prototype, "review_count_max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by affiliate status',
        enum: ['NONE', 'APPLIED', 'APPROVED', 'REJECTED'],
        example: 'APPROVED',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ListEntitiesDto.prototype, "affiliate_status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter entities that have affiliate/referral links available',
        type: Boolean,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (value === 'true' || value === true)
            return true;
        if (value === 'false' || value === false)
            return false;
        return undefined;
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ListEntitiesDto.prototype, "has_affiliate_link", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Entity type specific filters. Allows filtering by fields specific to each entity type (Course, Job, Hardware, Event, etc.)',
        type: entity_type_filters_dto_1.EntityTypeFiltersDto,
        example: {
            course: {
                skill_levels: ['BEGINNER', 'INTERMEDIATE'],
                certificate_available: true
            },
            job: {
                employment_types: ['Full-time', 'Remote'],
                salary_min: 80,
                salary_max: 150
            }
        }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => entity_type_filters_dto_1.EntityTypeFiltersDto),
    __metadata("design:type", entity_type_filters_dto_1.EntityTypeFiltersDto)
], ListEntitiesDto.prototype, "entity_type_filters", void 0);
//# sourceMappingURL=list-entities.dto.js.map