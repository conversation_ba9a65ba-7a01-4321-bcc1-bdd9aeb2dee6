"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityTypeFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
const course_filters_dto_1 = require("./course-filters.dto");
const job_filters_dto_1 = require("./job-filters.dto");
const hardware_filters_dto_1 = require("./hardware-filters.dto");
const event_filters_dto_1 = require("./event-filters.dto");
class EntityTypeFiltersDto {
}
exports.EntityTypeFiltersDto = EntityTypeFiltersDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Course entities',
        type: course_filters_dto_1.CourseFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => course_filters_dto_1.CourseFiltersDto),
    __metadata("design:type", course_filters_dto_1.CourseFiltersDto)
], EntityTypeFiltersDto.prototype, "course", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Job entities',
        type: job_filters_dto_1.JobFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => job_filters_dto_1.JobFiltersDto),
    __metadata("design:type", job_filters_dto_1.JobFiltersDto)
], EntityTypeFiltersDto.prototype, "job", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Hardware entities',
        type: hardware_filters_dto_1.HardwareFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => hardware_filters_dto_1.HardwareFiltersDto),
    __metadata("design:type", hardware_filters_dto_1.HardwareFiltersDto)
], EntityTypeFiltersDto.prototype, "hardware", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filters specific to Event entities',
        type: event_filters_dto_1.EventFiltersDto,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => event_filters_dto_1.EventFiltersDto),
    __metadata("design:type", event_filters_dto_1.EventFiltersDto)
], EntityTypeFiltersDto.prototype, "event", void 0);
//# sourceMappingURL=entity-type-filters.dto.js.map